import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:video_player/video_player.dart';
import 'dart:io';

import '../../core/app_theme.dart';
import '../../providers/app_state_provider.dart';
import '../../providers/ad_provider.dart';
import '../../services/video_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/file_picker_button.dart';
import '../../widgets/video_preview_widget.dart';

class VideoToWallpaperScreen extends StatefulWidget {
  const VideoToWallpaperScreen({super.key});

  @override
  State<VideoToWallpaperScreen> createState() => _VideoToWallpaperScreenState();
}

class _VideoToWallpaperScreenState extends State<VideoToWallpaperScreen> {
  String? _selectedVideoPath;
  VideoPlayerController? _videoController;
  bool _isHomeScreenSelected = true;
  bool _isLockScreenSelected = true;

  @override
  void dispose() {
    _videoController?.dispose();
    super.dispose();
  }

  Future<void> _pickVideo() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _selectedVideoPath = result.files.single.path;
        });

        await _initializeVideoPlayer();
      }
    } catch (e) {
      _showErrorSnackBar('Error picking video: $e');
    }
  }

  Future<void> _initializeVideoPlayer() async {
    if (_selectedVideoPath == null) return;

    _videoController?.dispose();
    _videoController = VideoPlayerController.file(
      File(_selectedVideoPath!),
    );

    try {
      await _videoController!.initialize();
      setState(() {});
    } catch (e) {
      _showErrorSnackBar('Error loading video: $e');
    }
  }

  Future<void> _setAsWallpaper() async {
    if (_selectedVideoPath == null) {
      _showErrorSnackBar('Please select a video first');
      return;
    }

    if (!_isHomeScreenSelected && !_isLockScreenSelected) {
      _showErrorSnackBar('Please select at least one screen option');
      return;
    }

    final appState = Provider.of<AppStateProvider>(context, listen: false);
    final adProvider = Provider.of<AdProvider>(context, listen: false);

    try {
      appState.setLoading(true, task: 'Setting video as wallpaper...');

      final success = await VideoService.setVideoAsWallpaper(
        videoPath: _selectedVideoPath!,
        setHomeScreen: _isHomeScreenSelected,
        setLockScreen: _isLockScreenSelected,
        onProgress: (progress) {
          appState.updateProgress(progress);
        },
      );

      appState.setLoading(false);

      if (success) {
        _showSuccessSnackBar('Video wallpaper set successfully!');

        // Show interstitial ad after successful completion
        await adProvider.showInterstitialAd();
      } else {
        _showErrorSnackBar('Failed to set video as wallpaper');
      }
    } catch (e) {
      appState.setLoading(false);
      _showErrorSnackBar('Error: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Video to Live Wallpaper',
        subtitle: 'Set videos as live wallpapers',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Video Picker
            FilePickerButton(
              title: 'Select Video',
              subtitle: 'Choose a video from your device',
              icon: Icons.video_library,
              onTap: _pickVideo,
              selectedFile: _selectedVideoPath,
            ),

            if (_selectedVideoPath != null) ...[
              const SizedBox(height: 24),

              // Video Preview
              VideoPreviewWidget(
                controller: _videoController,
                aspectRatio: 16 / 9,
              ),

              const SizedBox(height: 24),

              // Screen Options
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Wallpaper Options',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      CheckboxListTile(
                        title: const Text('Home Screen'),
                        subtitle: const Text('Set as home screen wallpaper'),
                        value: _isHomeScreenSelected,
                        onChanged: (value) {
                          setState(() {
                            _isHomeScreenSelected = value ?? false;
                          });
                        },
                        activeColor: AppTheme.primaryColor,
                      ),
                      CheckboxListTile(
                        title: const Text('Lock Screen'),
                        subtitle: const Text('Set as lock screen wallpaper'),
                        value: _isLockScreenSelected,
                        onChanged: (value) {
                          setState(() {
                            _isLockScreenSelected = value ?? false;
                          });
                        },
                        activeColor: AppTheme.primaryColor,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Set Wallpaper Button
              Consumer<AppStateProvider>(
                builder: (context, appState, child) {
                  return ElevatedButton(
                    onPressed: appState.isLoading ? null : _setAsWallpaper,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: appState.isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : const Text(
                            'Set as Live Wallpaper',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }
}
