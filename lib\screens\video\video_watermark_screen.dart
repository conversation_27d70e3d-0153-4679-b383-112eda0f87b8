import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:video_player/video_player.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';

import '../../core/app_theme.dart';
import '../../providers/app_state_provider.dart';
import '../../providers/ad_provider.dart';
import '../../services/video_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/file_picker_button.dart';
import '../../widgets/video_preview_widget.dart';

class VideoWatermarkScreen extends StatefulWidget {
  const VideoWatermarkScreen({super.key});

  @override
  State<VideoWatermarkScreen> createState() => _VideoWatermarkScreenState();
}

class _VideoWatermarkScreenState extends State<VideoWatermarkScreen> {
  String? _selectedVideoPath;
  String? _selectedImagePath;
  VideoPlayerController? _videoController;

  final TextEditingController _textController = TextEditingController();
  bool _useTextWatermark = true;
  double _watermarkX = 50.0;
  double _watermarkY = 50.0;
  double _opacity = 1.0;
  String? _outputVideoPath;

  @override
  void dispose() {
    _videoController?.dispose();
    _textController.dispose();
    super.dispose();
  }

  Future<void> _pickVideo() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _selectedVideoPath = result.files.single.path;
          _outputVideoPath = null; // Reset output when new video is selected
        });

        await _initializeVideoPlayer();
      }
    } catch (e) {
      _showErrorSnackBar('Error picking video: $e');
    }
  }

  Future<void> _pickWatermarkImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _selectedImagePath = result.files.single.path;
        });
      }
    } catch (e) {
      _showErrorSnackBar('Error picking image: $e');
    }
  }

  Future<void> _initializeVideoPlayer() async {
    if (_selectedVideoPath == null) return;

    _videoController?.dispose();
    _videoController = VideoPlayerController.file(
      File(_selectedVideoPath!),
    );

    try {
      await _videoController!.initialize();
      setState(() {});
    } catch (e) {
      _showErrorSnackBar('Error loading video: $e');
    }
  }

  Future<void> _addWatermark() async {
    if (_selectedVideoPath == null) {
      _showErrorSnackBar('Please select a video first');
      return;
    }

    if (_useTextWatermark && _textController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter watermark text');
      return;
    }

    if (!_useTextWatermark && _selectedImagePath == null) {
      _showErrorSnackBar('Please select a watermark image');
      return;
    }

    final appState = Provider.of<AppStateProvider>(context, listen: false);
    final adProvider = Provider.of<AdProvider>(context, listen: false);

    try {
      appState.setLoading(true, task: 'Adding watermark to video...');

      final outputPath = await VideoService.addWatermarkToVideo(
        inputPath: _selectedVideoPath!,
        watermarkText: _useTextWatermark ? _textController.text.trim() : null,
        watermarkImagePath: !_useTextWatermark ? _selectedImagePath : null,
        x: _watermarkX,
        y: _watermarkY,
        opacity: _opacity,
        onProgress: (progress) {
          appState.updateProgress(progress);
        },
      );

      appState.setLoading(false);

      if (outputPath != null) {
        setState(() {
          _outputVideoPath = outputPath;
        });

        _showSuccessSnackBar('Watermark added successfully!');

        // Show interstitial ad after successful completion
        await adProvider.showInterstitialAd();
      } else {
        _showErrorSnackBar('Failed to add watermark to video');
      }
    } catch (e) {
      appState.setLoading(false);
      _showErrorSnackBar('Error: $e');
    }
  }

  Future<void> _shareVideo() async {
    if (_outputVideoPath == null) return;

    try {
      await Share.shareXFiles(
        [XFile(_outputVideoPath!)],
        text: 'Video with watermark created using MediaCraft',
      );
    } catch (e) {
      _showErrorSnackBar('Error sharing video: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Video Watermark',
        subtitle: 'Add text or image watermarks to videos',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Video Picker
            FilePickerButton(
              title: 'Select Video',
              subtitle: 'Choose a video to add watermark',
              icon: Icons.video_library,
              onTap: _pickVideo,
              selectedFile: _selectedVideoPath,
              color: AppTheme.primaryColor,
            ),

            if (_selectedVideoPath != null) ...[
              const SizedBox(height: 24),

              // Video Preview
              VideoPreviewWidget(
                controller: _videoController,
                aspectRatio: 16 / 9,
              ),

              const SizedBox(height: 24),

              // Watermark Type Selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Watermark Type',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: RadioListTile<bool>(
                              title: const Text('Text'),
                              value: true,
                              groupValue: _useTextWatermark,
                              onChanged: (value) {
                                setState(() {
                                  _useTextWatermark = value ?? true;
                                });
                              },
                              activeColor: AppTheme.primaryColor,
                            ),
                          ),
                          Expanded(
                            child: RadioListTile<bool>(
                              title: const Text('Image'),
                              value: false,
                              groupValue: _useTextWatermark,
                              onChanged: (value) {
                                setState(() {
                                  _useTextWatermark = value ?? true;
                                });
                              },
                              activeColor: AppTheme.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Watermark Content
              if (_useTextWatermark) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Watermark Text',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 12),
                        TextField(
                          controller: _textController,
                          decoration: const InputDecoration(
                            hintText: 'Enter watermark text',
                            border: OutlineInputBorder(),
                          ),
                          maxLines: 2,
                        ),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                FilePickerButton(
                  title: 'Select Watermark Image',
                  subtitle: 'Choose an image for watermark',
                  icon: Icons.image,
                  onTap: _pickWatermarkImage,
                  selectedFile: _selectedImagePath,
                  color: AppTheme.secondaryColor,
                ),
              ],

              const SizedBox(height: 16),

              // Position and Opacity Controls
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Watermark Settings',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // X Position
                      Text(
                        'Horizontal Position: ${_watermarkX.toInt()}px',
                        style: const TextStyle(fontSize: 14),
                      ),
                      Slider(
                        value: _watermarkX,
                        min: 0,
                        max: 500,
                        divisions: 50,
                        onChanged: (value) {
                          setState(() {
                            _watermarkX = value;
                          });
                        },
                        activeColor: AppTheme.primaryColor,
                      ),

                      // Y Position
                      Text(
                        'Vertical Position: ${_watermarkY.toInt()}px',
                        style: const TextStyle(fontSize: 14),
                      ),
                      Slider(
                        value: _watermarkY,
                        min: 0,
                        max: 500,
                        divisions: 50,
                        onChanged: (value) {
                          setState(() {
                            _watermarkY = value;
                          });
                        },
                        activeColor: AppTheme.primaryColor,
                      ),

                      // Opacity
                      Text(
                        'Opacity: ${(_opacity * 100).toInt()}%',
                        style: const TextStyle(fontSize: 14),
                      ),
                      Slider(
                        value: _opacity,
                        min: 0.1,
                        max: 1.0,
                        divisions: 9,
                        onChanged: (value) {
                          setState(() {
                            _opacity = value;
                          });
                        },
                        activeColor: AppTheme.primaryColor,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Add Watermark Button
              Consumer<AppStateProvider>(
                builder: (context, appState, child) {
                  return ElevatedButton(
                    onPressed: appState.isLoading ? null : _addWatermark,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: appState.isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : const Text(
                            'Add Watermark',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  );
                },
              ),

              // Share Button (if video is processed)
              if (_outputVideoPath != null) ...[
                const SizedBox(height: 16),
                OutlinedButton.icon(
                  onPressed: _shareVideo,
                  icon: const Icon(Icons.share),
                  label: const Text('Share Video'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }
}
