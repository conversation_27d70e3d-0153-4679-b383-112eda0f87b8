import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart';

class PermissionService {
  static Future<void> requestInitialPermissions() async {
    try {
      // Request basic permissions needed for the app
      await [
        Permission.storage,
        Permission.manageExternalStorage,
        Permission.photos,
        Permission.videos,
        Permission.audio,
      ].request();
    } catch (e) {
      debugPrint('Error requesting initial permissions: $e');
    }
  }

  static Future<bool> requestStoragePermission() async {
    try {
      final status = await Permission.storage.request();
      if (status.isDenied) {
        final manageStatus = await Permission.manageExternalStorage.request();
        return manageStatus.isGranted;
      }
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting storage permission: $e');
      return false;
    }
  }

  static Future<bool> requestPhotosPermission() async {
    try {
      final status = await Permission.photos.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting photos permission: $e');
      return false;
    }
  }

  static Future<bool> requestVideosPermission() async {
    try {
      final status = await Permission.videos.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting videos permission: $e');
      return false;
    }
  }

  static Future<bool> requestAudioPermission() async {
    try {
      final status = await Permission.audio.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting audio permission: $e');
      return false;
    }
  }

  static Future<bool> requestMicrophonePermission() async {
    try {
      final status = await Permission.microphone.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting microphone permission: $e');
      return false;
    }
  }

  static Future<bool> hasStoragePermission() async {
    try {
      final status = await Permission.storage.status;
      if (status.isGranted) return true;
      
      final manageStatus = await Permission.manageExternalStorage.status;
      return manageStatus.isGranted;
    } catch (e) {
      debugPrint('Error checking storage permission: $e');
      return false;
    }
  }

  static Future<bool> hasPhotosPermission() async {
    try {
      final status = await Permission.photos.status;
      return status.isGranted;
    } catch (e) {
      debugPrint('Error checking photos permission: $e');
      return false;
    }
  }

  static Future<bool> hasVideosPermission() async {
    try {
      final status = await Permission.videos.status;
      return status.isGranted;
    } catch (e) {
      debugPrint('Error checking videos permission: $e');
      return false;
    }
  }

  static Future<bool> hasAudioPermission() async {
    try {
      final status = await Permission.audio.status;
      return status.isGranted;
    } catch (e) {
      debugPrint('Error checking audio permission: $e');
      return false;
    }
  }

  static Future<void> openAppSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      debugPrint('Error opening app settings: $e');
    }
  }
}
