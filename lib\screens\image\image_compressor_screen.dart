import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';

import '../../core/app_theme.dart';
import '../../providers/app_state_provider.dart';
import '../../providers/ad_provider.dart';
import '../../services/image_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/file_picker_button.dart';

class ImageCompressorScreen extends StatefulWidget {
  const ImageCompressorScreen({super.key});

  @override
  State<ImageCompressorScreen> createState() => _ImageCompressorScreenState();
}

class _ImageCompressorScreenState extends State<ImageCompressorScreen> {
  String? _selectedImagePath;
  String? _outputImagePath;
  int _targetSizeKB = 100;
  int _quality = 85;
  bool _optimizeForWeb = false;

  Future<void> _pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _selectedImagePath = result.files.single.path;
          _outputImagePath = null;
        });
      }
    } catch (e) {
      _showErrorSnackBar('Error picking image: $e');
    }
  }

  Future<void> _compressImage() async {
    if (_selectedImagePath == null) {
      _showErrorSnackBar('Please select an image first');
      return;
    }

    final appState = Provider.of<AppStateProvider>(context, listen: false);
    final adProvider = Provider.of<AdProvider>(context, listen: false);

    try {
      appState.setLoading(true, task: 'Compressing image...');

      final outputPath = await ImageService.compressImage(
        inputPath: _selectedImagePath!,
        targetSizeKB: _targetSizeKB,
        quality: _quality,
        optimizeForWeb: _optimizeForWeb,
        onProgress: (progress) {
          appState.updateProgress(progress);
        },
      );

      appState.setLoading(false);

      if (outputPath != null) {
        setState(() {
          _outputImagePath = outputPath;
        });
        
        _showSuccessSnackBar('Image compressed successfully!');
        await adProvider.showInterstitialAd();
      } else {
        _showErrorSnackBar('Failed to compress image');
      }
    } catch (e) {
      appState.setLoading(false);
      _showErrorSnackBar('Error: $e');
    }
  }

  Future<void> _shareImage() async {
    if (_outputImagePath == null) return;

    try {
      await Share.shareXFiles(
        [XFile(_outputImagePath!)],
        text: 'Image compressed using MediaCraft',
      );
    } catch (e) {
      _showErrorSnackBar('Error sharing image: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Image Compressor',
        subtitle: 'Reduce image file size',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Image Picker
            FilePickerButton(
              title: 'Select Image',
              subtitle: 'Choose an image to compress',
              icon: Icons.image,
              onTap: _pickImage,
              selectedFile: _selectedImagePath,
              color: AppTheme.warningColor,
            ),

            if (_selectedImagePath != null) ...[
              const SizedBox(height: 24),

              // Image Preview
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Image Preview',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Center(
                        child: Container(
                          constraints: const BoxConstraints(maxHeight: 200),
                          child: Image.file(
                            File(_selectedImagePath!),
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Compression Settings
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Compression Settings',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      Text('Target Size: ${_targetSizeKB}KB'),
                      Slider(
                        value: _targetSizeKB.toDouble(),
                        min: 10,
                        max: 1000,
                        divisions: 99,
                        onChanged: (value) {
                          setState(() {
                            _targetSizeKB = value.toInt();
                          });
                        },
                        activeColor: AppTheme.warningColor,
                      ),
                      
                      Text('Quality: $_quality%'),
                      Slider(
                        value: _quality.toDouble(),
                        min: 10,
                        max: 100,
                        divisions: 90,
                        onChanged: (value) {
                          setState(() {
                            _quality = value.toInt();
                          });
                        },
                        activeColor: AppTheme.warningColor,
                      ),
                      
                      CheckboxListTile(
                        title: const Text('Optimize for Web'),
                        subtitle: const Text('Apply web-specific optimizations'),
                        value: _optimizeForWeb,
                        onChanged: (value) {
                          setState(() {
                            _optimizeForWeb = value ?? false;
                          });
                        },
                        activeColor: AppTheme.warningColor,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Compress Button
              Consumer<AppStateProvider>(
                builder: (context, appState, child) {
                  return ElevatedButton(
                    onPressed: appState.isLoading ? null : _compressImage,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.warningColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: appState.isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : const Text(
                            'Compress Image',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  );
                },
              ),

              // Share Button (if image is compressed)
              if (_outputImagePath != null) ...[
                const SizedBox(height: 16),
                OutlinedButton.icon(
                  onPressed: _shareImage,
                  icon: const Icon(Icons.share),
                  label: const Text('Share Compressed Image'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }
}
