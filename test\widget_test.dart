// This is a basic Flutter widget test for MediaCraft app.

import 'package:flutter_test/flutter_test.dart';

import 'package:mediacraft/main.dart';

void main() {
  testWidgets('MediaCraft app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MediaCraftApp());

    // Verify that our app title is displayed.
    expect(find.text('MediaCraft'), findsOneWidget);

    // Verify that the subtitle is displayed.
    expect(find.text('All-in-one media toolkit'), findsOneWidget);
  });
}
