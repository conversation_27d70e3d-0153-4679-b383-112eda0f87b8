import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';

class FileService {
  /// Save file to device storage with proper permissions handling
  static Future<bool> saveToDevice({
    required String filePath,
    String? customFileName,
    String? subfolder,
  }) async {
    try {
      // Request storage permissions
      bool hasPermission = await _requestStoragePermission();
      if (!hasPermission) {
        debugPrint('Storage permission denied');
        return false;
      }

      final sourceFile = File(filePath);
      if (!await sourceFile.exists()) {
        debugPrint('Source file does not exist: $filePath');
        return false;
      }

      // Get the appropriate directory based on Android version
      Directory? targetDirectory;
      
      if (Platform.isAndroid) {
        // For Android 10+ (API 29+), use scoped storage
        if (await _isAndroid10OrHigher()) {
          targetDirectory = await _getPublicDirectory(filePath, subfolder);
        } else {
          // For older Android versions, use external storage
          targetDirectory = Directory('/storage/emulated/0/Download');
        }
      } else if (Platform.isIOS) {
        // For iOS, save to Documents directory
        targetDirectory = await getApplicationDocumentsDirectory();
      }

      if (targetDirectory == null) {
        debugPrint('Could not determine target directory');
        return false;
      }

      // Create subfolder if specified
      if (subfolder != null) {
        targetDirectory = Directory('${targetDirectory.path}/$subfolder');
        if (!await targetDirectory.exists()) {
          await targetDirectory.create(recursive: true);
        }
      }

      // Generate filename
      final fileName = customFileName ?? _generateFileName(filePath);
      final targetPath = '${targetDirectory.path}/$fileName';

      // Copy file to target location
      await sourceFile.copy(targetPath);
      
      debugPrint('File saved successfully to: $targetPath');
      return true;
    } catch (e) {
      debugPrint('Error saving file to device: $e');
      return false;
    }
  }

  /// Request appropriate storage permissions based on Android version
  static Future<bool> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      // Check Android version
      if (await _isAndroid10OrHigher()) {
        // Android 10+ uses scoped storage, no special permission needed for app-specific directories
        // But we might need MANAGE_EXTERNAL_STORAGE for full access
        final status = await Permission.manageExternalStorage.request();
        if (status.isGranted) {
          return true;
        }
        
        // Fallback to basic storage permission
        final basicStatus = await Permission.storage.request();
        return basicStatus.isGranted;
      } else {
        // Android 9 and below
        final status = await Permission.storage.request();
        return status.isGranted;
      }
    } else if (Platform.isIOS) {
      // iOS doesn't need explicit storage permission for app documents
      return true;
    }
    
    return false;
  }

  /// Check if device is running Android 10 or higher
  static Future<bool> _isAndroid10OrHigher() async {
    if (Platform.isAndroid) {
      // This is a simplified check - in a real app you'd use device_info_plus
      return true; // Assume modern Android for now
    }
    return false;
  }

  /// Get appropriate public directory based on file type
  static Future<Directory?> _getPublicDirectory(String filePath, String? subfolder) async {
    try {
      final extension = filePath.split('.').last.toLowerCase();
      String basePath = '/storage/emulated/0';
      
      // Determine directory based on file type
      if (['mp3', 'wav', 'aac', 'm4a', 'ogg'].contains(extension)) {
        basePath += '/Music';
      } else if (['mp4', 'avi', 'mkv', 'mov', '3gp'].contains(extension)) {
        basePath += '/Movies';
      } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension)) {
        basePath += '/Pictures';
      } else if (['pdf'].contains(extension)) {
        basePath += '/Documents';
      } else {
        basePath += '/Download';
      }
      
      // Add app-specific subfolder
      if (subfolder != null) {
        basePath += '/$subfolder';
      } else {
        basePath += '/MediaCraft';
      }
      
      final directory = Directory(basePath);
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      
      return directory;
    } catch (e) {
      debugPrint('Error getting public directory: $e');
      // Fallback to Downloads
      return Directory('/storage/emulated/0/Download');
    }
  }

  /// Generate a unique filename if one isn't provided
  static String _generateFileName(String originalPath) {
    final file = File(originalPath);
    final extension = file.path.split('.').last;
    final baseName = file.path.split('/').last.split('.').first;
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return '${baseName}_$timestamp.$extension';
  }

  /// Get file size in a human-readable format
  static String getFileSize(String filePath) {
    try {
      final file = File(filePath);
      final bytes = file.lengthSync();
      
      if (bytes < 1024) {
        return '$bytes B';
      } else if (bytes < 1024 * 1024) {
        return '${(bytes / 1024).toStringAsFixed(1)} KB';
      } else if (bytes < 1024 * 1024 * 1024) {
        return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
      } else {
        return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
      }
    } catch (e) {
      return 'Unknown';
    }
  }

  /// Check if file exists
  static Future<bool> fileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// Delete file
  static Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting file: $e');
      return false;
    }
  }

  /// Get file info
  static Future<Map<String, dynamic>?> getFileInfo(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return null;
      }
      
      final stat = await file.stat();
      
      return {
        'path': filePath,
        'name': file.path.split('/').last,
        'size': stat.size,
        'sizeFormatted': getFileSize(filePath),
        'modified': stat.modified.toIso8601String(),
        'type': file.path.split('.').last.toLowerCase(),
      };
    } catch (e) {
      debugPrint('Error getting file info: $e');
      return null;
    }
  }
}
