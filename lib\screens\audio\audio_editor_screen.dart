import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:share_plus/share_plus.dart';

import '../../core/app_theme.dart';
import '../../providers/app_state_provider.dart';
import '../../providers/ad_provider.dart';
import '../../services/audio_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/file_picker_button.dart';

class AudioEditorScreen extends StatefulWidget {
  const AudioEditorScreen({super.key});

  @override
  State<AudioEditorScreen> createState() => _AudioEditorScreenState();
}

class _AudioEditorScreenState extends State<AudioEditorScreen> {
  String? _selectedAudioPath;
  AudioPlayer? _audioPlayer;
  String? _outputAudioPath;

  double _reverbRoomSize = 0.5;
  double _reverbDamping = 0.5;
  double _reverbWetLevel = 0.3;
  double _speedFactor = 0.5;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
  }

  @override
  void dispose() {
    _audioPlayer?.dispose();
    super.dispose();
  }

  Future<void> _pickAudio() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _selectedAudioPath = result.files.single.path;
          _outputAudioPath = null;
        });
      }
    } catch (e) {
      _showErrorSnackBar('Error picking audio: $e');
    }
  }

  Future<void> _applyReverbEffect() async {
    if (_selectedAudioPath == null) {
      _showErrorSnackBar('Please select an audio file first');
      return;
    }

    final appState = Provider.of<AppStateProvider>(context, listen: false);
    final adProvider = Provider.of<AdProvider>(context, listen: false);

    try {
      appState.setLoading(true, task: 'Applying reverb effect...');

      final outputPath = await AudioService.applyReverbEffect(
        inputPath: _selectedAudioPath!,
        roomSize: _reverbRoomSize,
        damping: _reverbDamping,
        wetLevel: _reverbWetLevel,
        onProgress: (progress) {
          appState.updateProgress(progress);
        },
      );

      appState.setLoading(false);

      if (outputPath != null) {
        setState(() {
          _outputAudioPath = outputPath;
        });

        _showSuccessSnackBar('Reverb effect applied successfully!');
        await adProvider.showInterstitialAd();
      } else {
        _showErrorSnackBar('Failed to apply reverb effect');
      }
    } catch (e) {
      appState.setLoading(false);
      _showErrorSnackBar('Error: $e');
    }
  }

  Future<void> _applySlowMotionEffect() async {
    if (_selectedAudioPath == null) {
      _showErrorSnackBar('Please select an audio file first');
      return;
    }

    final appState = Provider.of<AppStateProvider>(context, listen: false);
    final adProvider = Provider.of<AdProvider>(context, listen: false);

    try {
      appState.setLoading(true, task: 'Applying slow motion effect...');

      final outputPath = await AudioService.applySlowMotionEffect(
        inputPath: _selectedAudioPath!,
        speed: _speedFactor,
        onProgress: (progress) {
          appState.updateProgress(progress);
        },
      );

      appState.setLoading(false);

      if (outputPath != null) {
        setState(() {
          _outputAudioPath = outputPath;
        });

        _showSuccessSnackBar('Speed effect applied successfully!');
        await adProvider.showInterstitialAd();
      } else {
        _showErrorSnackBar('Failed to apply speed effect');
      }
    } catch (e) {
      appState.setLoading(false);
      _showErrorSnackBar('Error: $e');
    }
  }

  Future<void> _reverseAudio() async {
    if (_selectedAudioPath == null) {
      _showErrorSnackBar('Please select an audio file first');
      return;
    }

    final appState = Provider.of<AppStateProvider>(context, listen: false);
    final adProvider = Provider.of<AdProvider>(context, listen: false);

    try {
      appState.setLoading(true, task: 'Reversing audio...');

      final outputPath = await AudioService.reverseAudio(
        inputPath: _selectedAudioPath!,
        onProgress: (progress) {
          appState.updateProgress(progress);
        },
      );

      appState.setLoading(false);

      if (outputPath != null) {
        setState(() {
          _outputAudioPath = outputPath;
        });

        _showSuccessSnackBar('Audio reversed successfully!');
        await adProvider.showInterstitialAd();
      } else {
        _showErrorSnackBar('Failed to reverse audio');
      }
    } catch (e) {
      appState.setLoading(false);
      _showErrorSnackBar('Error: $e');
    }
  }

  Future<void> _shareAudio() async {
    if (_outputAudioPath == null) return;

    try {
      await Share.shareXFiles(
        [XFile(_outputAudioPath!)],
        text: 'Audio edited using MediaCraft',
      );
    } catch (e) {
      _showErrorSnackBar('Error sharing audio: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Simple Audio Editor',
        subtitle: 'Apply effects to your audio files',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Audio Picker
            FilePickerButton(
              title: 'Select Audio File',
              subtitle: 'Choose an audio file to edit',
              icon: Icons.audiotrack,
              onTap: _pickAudio,
              selectedFile: _selectedAudioPath,
              color: AppTheme.successColor,
            ),

            if (_selectedAudioPath != null) ...[
              const SizedBox(height: 24),

              // Reverb Effect
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Reverb Effect',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text('Room Size: ${(_reverbRoomSize * 100).toInt()}%'),
                      Slider(
                        value: _reverbRoomSize,
                        onChanged: (value) {
                          setState(() {
                            _reverbRoomSize = value;
                          });
                        },
                        activeColor: AppTheme.primaryColor,
                      ),
                      Text('Damping: ${(_reverbDamping * 100).toInt()}%'),
                      Slider(
                        value: _reverbDamping,
                        onChanged: (value) {
                          setState(() {
                            _reverbDamping = value;
                          });
                        },
                        activeColor: AppTheme.primaryColor,
                      ),
                      Text('Wet Level: ${(_reverbWetLevel * 100).toInt()}%'),
                      Slider(
                        value: _reverbWetLevel,
                        onChanged: (value) {
                          setState(() {
                            _reverbWetLevel = value;
                          });
                        },
                        activeColor: AppTheme.primaryColor,
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _applyReverbEffect,
                          child: const Text('Apply Reverb'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Speed Effect
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Speed Effect',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text('Speed: ${(_speedFactor * 100).toInt()}%'),
                      Slider(
                        value: _speedFactor,
                        min: 0.25,
                        max: 2.0,
                        divisions: 7,
                        onChanged: (value) {
                          setState(() {
                            _speedFactor = value;
                          });
                        },
                        activeColor: AppTheme.primaryColor,
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _applySlowMotionEffect,
                          child: const Text('Apply Speed Effect'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Reverse Audio
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Reverse Audio',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Reverse the entire audio file',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _reverseAudio,
                          child: const Text('Reverse Audio'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Share Button (if audio is processed)
              if (_outputAudioPath != null) ...[
                const SizedBox(height: 24),
                OutlinedButton.icon(
                  onPressed: _shareAudio,
                  icon: const Icon(Icons.share),
                  label: const Text('Share Edited Audio'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }
}
