import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';

import '../../core/app_theme.dart';
import '../../providers/app_state_provider.dart';
import '../../providers/ad_provider.dart';
import '../../services/pdf_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/file_picker_button.dart';

class PdfConverterScreen extends StatefulWidget {
  const PdfConverterScreen({super.key});

  @override
  State<PdfConverterScreen> createState() => _PdfConverterScreenState();
}

class _PdfConverterScreenState extends State<PdfConverterScreen> {
  List<String> _selectedImagePaths = [];
  String? _selectedPdfPath;
  String? _outputPath;
  bool _isImagesToPdf = true;

  Future<void> _pickImages() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: true,
      );

      if (result != null) {
        setState(() {
          _selectedImagePaths = result.files
              .where((file) => file.path != null)
              .map((file) => file.path!)
              .toList();
          _outputPath = null;
        });
      }
    } catch (e) {
      _showErrorSnackBar('Error picking images: $e');
    }
  }

  Future<void> _pickPdf() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _selectedPdfPath = result.files.single.path;
          _outputPath = null;
        });
      }
    } catch (e) {
      _showErrorSnackBar('Error picking PDF: $e');
    }
  }

  Future<void> _convertImagesToPdf() async {
    if (_selectedImagePaths.isEmpty) {
      _showErrorSnackBar('Please select images first');
      return;
    }

    final appState = Provider.of<AppStateProvider>(context, listen: false);
    final adProvider = Provider.of<AdProvider>(context, listen: false);

    try {
      appState.setLoading(true, task: 'Converting images to PDF...');

      final outputPath = await PdfService.convertImagesToPdf(
        imagePaths: _selectedImagePaths,
        onProgress: (progress) {
          appState.updateProgress(progress);
        },
      );

      appState.setLoading(false);

      if (outputPath != null) {
        setState(() {
          _outputPath = outputPath;
        });

        _showSuccessSnackBar('PDF created successfully!');
        await adProvider.showInterstitialAd();
      } else {
        _showErrorSnackBar('Failed to create PDF');
      }
    } catch (e) {
      appState.setLoading(false);
      _showErrorSnackBar('Error: $e');
    }
  }

  Future<void> _convertPdfToImages() async {
    if (_selectedPdfPath == null) {
      _showErrorSnackBar('Please select a PDF first');
      return;
    }

    final appState = Provider.of<AppStateProvider>(context, listen: false);
    final adProvider = Provider.of<AdProvider>(context, listen: false);

    try {
      appState.setLoading(true, task: 'Converting PDF to images...');

      final outputPaths = await PdfService.convertPdfToImages(
        pdfPath: _selectedPdfPath!,
        onProgress: (progress) {
          appState.updateProgress(progress);
        },
      );

      appState.setLoading(false);

      if (outputPaths != null && outputPaths.isNotEmpty) {
        setState(() {
          _outputPath = outputPaths.first; // For simplicity, show first image
        });

        _showSuccessSnackBar('Images created successfully!');
        await adProvider.showInterstitialAd();
      } else {
        _showErrorSnackBar('Failed to convert PDF to images');
      }
    } catch (e) {
      appState.setLoading(false);
      _showErrorSnackBar('Error: $e');
    }
  }

  Future<void> _shareFile() async {
    if (_outputPath == null) return;

    try {
      await Share.shareXFiles(
        [XFile(_outputPath!)],
        text: 'File converted using MediaCraft',
      );
    } catch (e) {
      _showErrorSnackBar('Error sharing file: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'PDF Converter',
        subtitle: 'Convert images to PDF and PDF to images',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Conversion Type Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Conversion Type',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: RadioListTile<bool>(
                            title: const Text('Images to PDF'),
                            value: true,
                            groupValue: _isImagesToPdf,
                            onChanged: (value) {
                              setState(() {
                                _isImagesToPdf = value ?? true;
                                _selectedImagePaths.clear();
                                _selectedPdfPath = null;
                                _outputPath = null;
                              });
                            },
                            activeColor: AppTheme.secondaryColor,
                          ),
                        ),
                        Expanded(
                          child: RadioListTile<bool>(
                            title: const Text('PDF to Images'),
                            value: false,
                            groupValue: _isImagesToPdf,
                            onChanged: (value) {
                              setState(() {
                                _isImagesToPdf = value ?? true;
                                _selectedImagePaths.clear();
                                _selectedPdfPath = null;
                                _outputPath = null;
                              });
                            },
                            activeColor: AppTheme.secondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // File Picker
            if (_isImagesToPdf) ...[
              FilePickerButton(
                title: 'Select Images',
                subtitle: _selectedImagePaths.isEmpty
                    ? 'Choose images to convert to PDF'
                    : '${_selectedImagePaths.length} images selected',
                icon: Icons.image,
                onTap: _pickImages,
                selectedFile:
                    _selectedImagePaths.isNotEmpty ? 'Multiple images' : null,
                color: AppTheme.secondaryColor,
              ),
            ] else ...[
              FilePickerButton(
                title: 'Select PDF',
                subtitle: 'Choose a PDF to convert to images',
                icon: Icons.picture_as_pdf,
                onTap: _pickPdf,
                selectedFile: _selectedPdfPath,
                color: AppTheme.secondaryColor,
              ),
            ],

            if ((_isImagesToPdf && _selectedImagePaths.isNotEmpty) ||
                (!_isImagesToPdf && _selectedPdfPath != null)) ...[
              const SizedBox(height: 32),

              // Convert Button
              Consumer<AppStateProvider>(
                builder: (context, appState, child) {
                  return ElevatedButton(
                    onPressed: appState.isLoading
                        ? null
                        : (_isImagesToPdf
                            ? _convertImagesToPdf
                            : _convertPdfToImages),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.secondaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: appState.isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : Text(
                            _isImagesToPdf ? 'Create PDF' : 'Extract Images',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  );
                },
              ),

              // Share Button (if file is converted)
              if (_outputPath != null) ...[
                const SizedBox(height: 16),
                OutlinedButton.icon(
                  onPressed: _shareFile,
                  icon: const Icon(Icons.share),
                  label: const Text('Share Converted File'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }
}
