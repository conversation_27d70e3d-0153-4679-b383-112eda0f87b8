# Optimized for 7GB RAM system - Conservative memory allocation
org.gradle.jvmargs=-Xmx2048m -Xms512m -XX:MaxMetaspaceSize=512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=16m -Dfile.encoding=UTF-8 -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.workers.max=2
org.gradle.configureondemand=true
android.enableR8.fullMode=false
android.enableD8.desugaring=true
