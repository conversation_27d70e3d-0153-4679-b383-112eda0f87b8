import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';

import '../../core/app_theme.dart';
import '../../providers/app_state_provider.dart';
import '../../providers/ad_provider.dart';
import '../../services/image_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/file_picker_button.dart';

class ImageConverterScreen extends StatefulWidget {
  const ImageConverterScreen({super.key});

  @override
  State<ImageConverterScreen> createState() => _ImageConverterScreenState();
}

class _ImageConverterScreenState extends State<ImageConverterScreen> {
  String? _selectedImagePath;
  String? _outputImagePath;
  String _outputFormat = 'jpg';
  int _quality = 90;

  final List<String> _formats = ['jpg', 'png', 'webp', 'bmp'];

  Future<void> _pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _selectedImagePath = result.files.single.path;
          _outputImagePath = null;
        });
      }
    } catch (e) {
      _showErrorSnackBar('Error picking image: $e');
    }
  }

  Future<void> _convertImage() async {
    if (_selectedImagePath == null) {
      _showErrorSnackBar('Please select an image first');
      return;
    }

    final appState = Provider.of<AppStateProvider>(context, listen: false);
    final adProvider = Provider.of<AdProvider>(context, listen: false);

    try {
      appState.setLoading(true, task: 'Converting image...');

      final outputPath = await ImageService.convertImageFormat(
        inputPath: _selectedImagePath!,
        outputFormat: _outputFormat,
        quality: _quality,
        onProgress: (progress) {
          appState.updateProgress(progress);
        },
      );

      appState.setLoading(false);

      if (outputPath != null) {
        setState(() {
          _outputImagePath = outputPath;
        });
        
        _showSuccessSnackBar('Image converted successfully!');
        await adProvider.showInterstitialAd();
      } else {
        _showErrorSnackBar('Failed to convert image');
      }
    } catch (e) {
      appState.setLoading(false);
      _showErrorSnackBar('Error: $e');
    }
  }

  Future<void> _shareImage() async {
    if (_outputImagePath == null) return;

    try {
      await Share.shareXFiles(
        [XFile(_outputImagePath!)],
        text: 'Image converted using MediaCraft',
      );
    } catch (e) {
      _showErrorSnackBar('Error sharing image: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Image Converter',
        subtitle: 'Convert between image formats',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Image Picker
            FilePickerButton(
              title: 'Select Image',
              subtitle: 'Choose an image to convert',
              icon: Icons.transform,
              onTap: _pickImage,
              selectedFile: _selectedImagePath,
              color: AppTheme.errorColor,
            ),

            if (_selectedImagePath != null) ...[
              const SizedBox(height: 24),

              // Image Preview
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Image Preview',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Center(
                        child: Container(
                          constraints: const BoxConstraints(maxHeight: 200),
                          child: Image.file(
                            File(_selectedImagePath!),
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Conversion Settings
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Conversion Settings',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      const Text('Output Format:'),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        value: _outputFormat,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                        ),
                        items: _formats.map((format) {
                          return DropdownMenuItem(
                            value: format,
                            child: Text(format.toUpperCase()),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _outputFormat = value!;
                          });
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      Text('Quality: $_quality%'),
                      Slider(
                        value: _quality.toDouble(),
                        min: 10,
                        max: 100,
                        divisions: 90,
                        onChanged: (value) {
                          setState(() {
                            _quality = value.toInt();
                          });
                        },
                        activeColor: AppTheme.errorColor,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Convert Button
              Consumer<AppStateProvider>(
                builder: (context, appState, child) {
                  return ElevatedButton(
                    onPressed: appState.isLoading ? null : _convertImage,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.errorColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: appState.isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : const Text(
                            'Convert Image',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  );
                },
              ),

              // Share Button (if image is converted)
              if (_outputImagePath != null) ...[
                const SizedBox(height: 16),
                OutlinedButton.icon(
                  onPressed: _shareImage,
                  icon: const Icon(Icons.share),
                  label: const Text('Share Converted Image'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }
}
