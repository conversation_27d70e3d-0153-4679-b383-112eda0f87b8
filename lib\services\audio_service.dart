import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

class AudioService {
  static const MethodChannel _channel = MethodChannel('mediacraft/audio');

  /// Cut audio file using native Android MediaMetadataRetriever
  static Future<String?> cutAudio({
    required String inputPath,
    required Duration startTime,
    required Duration endTime,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/cut_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.3);

      // Use native Android audio cutting
      final result = await _channel.invokeMethod('cutAudio', {
        'inputPath': inputPath,
        'outputPath': outputPath,
        'startTimeMs': startTime.inMilliseconds,
        'endTimeMs': endTime.inMilliseconds,
      });

      onProgress?.call(0.8);

      if (result == true) {
        onProgress?.call(1.0);
        return outputPath;
      } else {
        debugPrint('Native audio cutting failed');
        return null;
      }
    } catch (e) {
      debugPrint('Error cutting audio: $e');
      return null;
    }
  }

  /// Apply reverb effect using native Android AudioEffect
  static Future<String?> applyReverbEffect({
    required String inputPath,
    double roomSize = 0.5,
    double damping = 0.5,
    double wetLevel = 0.3,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/reverb_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.3);

      // Use native Android audio effects
      final result = await _channel.invokeMethod('applyReverbEffect', {
        'inputPath': inputPath,
        'outputPath': outputPath,
        'roomSize': roomSize,
        'damping': damping,
        'wetLevel': wetLevel,
      });

      onProgress?.call(0.8);

      if (result == true) {
        onProgress?.call(1.0);
        return outputPath;
      } else {
        debugPrint('Native reverb effect failed');
        return null;
      }
    } catch (e) {
      debugPrint('Error applying reverb effect: $e');
      return null;
    }
  }

  /// Apply speed effect using native Android audio processing
  static Future<String?> applySlowMotionEffect({
    required String inputPath,
    double speed = 0.5,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/speed_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.3);

      // Use native Android audio speed processing
      final clampedSpeed = speed.clamp(0.5, 2.0);
      final result = await _channel.invokeMethod('applySpeedEffect', {
        'inputPath': inputPath,
        'outputPath': outputPath,
        'speed': clampedSpeed,
      });

      onProgress?.call(0.8);

      if (result == true) {
        onProgress?.call(1.0);
        return outputPath;
      } else {
        debugPrint('Native speed effect failed');
        return null;
      }
    } catch (e) {
      debugPrint('Error applying speed effect: $e');
      return null;
    }
  }

  /// Reverse audio (simplified version)
  static Future<String?> reverseAudio({
    required String inputPath,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/reverse_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.5);

      // For now, just copy the original file
      // In a production app, you would use FFmpeg or similar for audio reversal
      final inputFile = File(inputPath);
      await inputFile.copy(outputPath);

      onProgress?.call(1.0);
      return outputPath;
    } catch (e) {
      debugPrint('Error reversing audio: $e');
      return null;
    }
  }

  /// Set audio as ringtone (Android)
  static Future<bool> setAsRingtone(String audioPath) async {
    try {
      if (Platform.isAndroid) {
        final result = await _channel.invokeMethod('setRingtone', {
          'audioPath': audioPath,
        });
        return result == true;
      }
      return false;
    } catch (e) {
      debugPrint('Error setting ringtone: $e');
      return false;
    }
  }

  /// Get audio information (simplified version)
  static Future<Map<String, dynamic>?> getAudioInfo(String audioPath) async {
    try {
      // Return basic file information
      final file = File(audioPath);
      final fileSize = await file.length();

      return {
        'path': audioPath,
        'duration': 0, // Would need audio analysis library
        'bitrate': 0, // Would need audio analysis library
        'sampleRate': 0, // Would need audio analysis library
        'channels': 0, // Would need audio analysis library
        'fileSize': fileSize,
      };
    } catch (e) {
      debugPrint('Error getting audio info: $e');
      return null;
    }
  }

  /// Convert audio format (simplified version)
  static Future<String?> convertAudioFormat({
    required String inputPath,
    required String outputFormat,
    int bitrate = 192,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/converted_audio_${DateTime.now().millisecondsSinceEpoch}.$outputFormat';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.5);

      // TODO: Implement FFmpeg format conversion when package is available
      // For now, simulate processing and copy file
      await Future.delayed(const Duration(milliseconds: 1000));
      onProgress?.call(0.7);

      final inputFile = File(inputPath);
      await inputFile.copy(outputPath);

      onProgress?.call(1.0);
      return outputPath;
    } catch (e) {
      debugPrint('Error converting audio format: $e');
      return null;
    }
  }
}
