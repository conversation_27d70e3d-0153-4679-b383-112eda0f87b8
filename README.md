# MediaCraft - All-in-One Media Toolkit

A comprehensive Flutter application that provides a variety of useful media processing tools in one place. MediaCraft offers excellent performance, beautiful UI, and easy navigation for video, audio, image, and PDF processing tasks.

## Features

### 🎥 Video Tools
- **Video to Live Wallpaper**: Convert videos to live wallpapers for home and lock screen
- **Video Watermark**: Add text or image watermarks to videos with lossless quality

### 🎵 Audio Tools
- **MP3 Cutter & Ringtone Maker**: Cut audio files and create custom ringtones
- **Simple Audio Editor**: Apply effects like reverb, slow motion, and reverse

### 🖼️ Image Tools
- **Image Compressor**: Reduce image file size with custom target sizes
- **Image Converter**: Convert between different image formats (PNG, JPG, WEBP, BMP)

### 📄 PDF Tools
- **PDF Converter**: Convert images to PDF and PDF to images
- **PDF Compressor**: Reduce PDF file size while maintaining quality

### 💰 Monetization
- **Native Ads**: In-line ads between tool options on the home screen
- **Interstitial Ads**: Full-screen ads after task completion

## Project Structure

```
lib/
├── core/
│   ├── app_theme.dart          # App theming and colors
│   └── app_router.dart         # Navigation routing
├── models/
│   └── tool_model.dart         # Data models for tools
├── providers/
│   ├── app_state_provider.dart # Global app state management
│   └── ad_provider.dart        # Ad management
├── screens/
│   ├── home/
│   │   └── home_screen.dart    # Main home screen
│   ├── video/
│   │   ├── video_to_wallpaper_screen.dart
│   │   └── video_watermark_screen.dart
│   ├── audio/
│   │   ├── mp3_cutter_screen.dart
│   │   └── audio_editor_screen.dart
│   ├── image/
│   │   ├── image_compressor_screen.dart
│   │   └── image_converter_screen.dart
│   └── pdf/
│       ├── pdf_converter_screen.dart
│       └── pdf_compressor_screen.dart
├── services/
│   ├── video_service.dart      # Video processing logic
│   ├── audio_service.dart      # Audio processing logic
│   ├── image_service.dart      # Image processing logic
│   ├── pdf_service.dart        # PDF processing logic
│   └── permission_service.dart # Permission handling
├── widgets/
│   ├── tool_card.dart          # Tool display cards
│   ├── native_ad_widget.dart   # Native ad display
│   ├── app_header.dart         # App header component
│   ├── category_filter.dart    # Category filtering
│   ├── custom_app_bar.dart     # Custom app bar
│   ├── file_picker_button.dart # File selection button
│   └── video_preview_widget.dart # Video preview component
└── main.dart                   # App entry point
```

## Setup Instructions

### Prerequisites
- Flutter SDK (>=3.10.0)
- Android Studio / VS Code
- Android SDK (API level 21+)
- Dart SDK (>=3.0.0)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mediacraft
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure AdMob**
   - Replace the test Ad Unit IDs in `lib/providers/ad_provider.dart` with your actual AdMob Ad Unit IDs
   - Update the AdMob App ID in `android/app/src/main/AndroidManifest.xml`

4. **Android Setup**
   - Ensure minimum SDK version is 21 in `android/app/build.gradle`
   - The required permissions are already configured in `AndroidManifest.xml`

5. **Run the app**
   ```bash
   flutter run
   ```

## Key Dependencies

### Core Dependencies
- `flutter`: Flutter framework
- `provider`: State management
- `cupertino_icons`: iOS-style icons

### UI & Animation
- `flutter_staggered_grid_view`: Staggered grid layouts
- `animations`: Smooth transitions
- `lottie`: Animation support

### File Handling
- `file_picker`: File selection
- `path_provider`: Path utilities
- `permission_handler`: Permission management
- `share_plus`: File sharing

### Media Processing
- `ffmpeg_kit_flutter`: Video/audio processing
- `video_player`: Video playback
- `audioplayers`: Audio playback
- `audio_waveforms`: Audio waveform display
- `image`: Image processing
- `flutter_image_compress`: Image compression

### PDF Processing
- `pdf`: PDF creation
- `printing`: PDF utilities
- `syncfusion_flutter_pdf`: Advanced PDF operations

### Ads
- `google_mobile_ads`: Google AdMob integration

### Storage
- `shared_preferences`: Local preferences
- `sqflite`: Local database

## Features Implementation

### Video Processing
- **Lossless Watermarking**: Uses FFmpeg with ultrafast preset and CRF 0 for lossless quality
- **Live Wallpaper**: Optimizes videos for wallpaper format and uses Android intents

### Audio Processing
- **Precise Cutting**: Uses FFmpeg with copy codec for fast, lossless cutting
- **Effects**: Implements reverb, speed adjustment, and reverse effects
- **Ringtone Setting**: Native Android integration for setting ringtones

### Image Processing
- **Smart Compression**: Iterative quality adjustment to reach target file sizes
- **Format Conversion**: Supports PNG, JPG, WEBP, BMP formats
- **Batch Processing**: Process multiple images simultaneously

### PDF Processing
- **Image to PDF**: Creates multi-page PDFs from image collections
- **PDF to Images**: Extracts each page as a separate image
- **Compression**: Multiple compression levels for size optimization

### Ad Integration
- **Native Ads**: Seamlessly integrated between tool cards
- **Interstitial Ads**: Shown after successful task completion
- **Smart Loading**: Preloads ads for smooth user experience

## Performance Optimizations

1. **Lazy Loading**: Tools and ads are loaded on demand
2. **Memory Management**: Proper disposal of controllers and resources
3. **Background Processing**: Heavy operations run in background with progress indicators
4. **Caching**: Temporary files are managed efficiently
5. **Optimized UI**: Smooth animations and responsive design

## Security Features

1. **Permission Handling**: Proper runtime permission requests
2. **File Validation**: Input validation for all file operations
3. **Secure Storage**: Safe handling of temporary files
4. **Error Handling**: Comprehensive error catching and user feedback

## Customization

### Adding New Tools
1. Create a new screen in the appropriate category folder
2. Add the tool to `tool_model.dart`
3. Implement the processing logic in the relevant service
4. Update the router in `app_router.dart`

### Modifying UI Theme
- Update colors and styles in `core/app_theme.dart`
- Customize animations in individual widgets
- Modify layouts in screen files

### Ad Configuration
- Update Ad Unit IDs in `ad_provider.dart`
- Adjust ad placement frequency in `home_screen.dart`
- Configure ad loading strategies

## Building for Release

### Android
```bash
flutter build apk --release
# or for app bundle
flutter build appbundle --release
```

### iOS (if needed)
```bash
flutter build ios --release
```

## Troubleshooting

### Common Issues
1. **Permission Errors**: Ensure all permissions are granted in device settings
2. **FFmpeg Errors**: Check file formats and paths
3. **Ad Loading Issues**: Verify AdMob configuration and internet connection
4. **File Access Issues**: Check storage permissions and file paths

### Debug Mode
Run with verbose logging:
```bash
flutter run --verbose
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the troubleshooting section

---

**MediaCraft** - Your all-in-one media processing toolkit! 🚀
