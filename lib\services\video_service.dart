import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
// import 'package:ffmpeg_kit_flutter/return_code.dart';

class VideoService {
  // static const MethodChannel _channel = MethodChannel('mediacraft/wallpaper'); // Commented out as not used yet

  /// Set video as live wallpaper
  static Future<bool> setVideoAsWallpaper({
    required String videoPath,
    required bool setHomeScreen,
    required bool setLockScreen,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      // For Android, we need to convert video to a format suitable for live wallpaper
      final outputPath = await _prepareVideoForWallpaper(
        videoPath,
        onProgress: (progress) => onProgress?.call(0.1 + (progress * 0.7)),
      );

      onProgress?.call(0.8);

      if (outputPath == null) {
        return false;
      }

      // Set as wallpaper using Android Intent
      final success = await _setWallpaperViaIntent(
        outputPath,
        setHomeScreen,
        setLockScreen,
      );

      onProgress?.call(1.0);
      return success;
    } catch (e) {
      debugPrint('Error setting video as wallpaper: $e');
      return false;
    }
  }

  /// Prepare video for wallpaper (optimize format and size)
  static Future<String?> _prepareVideoForWallpaper(String inputPath,
      {Function(double)? onProgress}) async {
    try {
      final directory = await getTemporaryDirectory();
      final outputPath = '${directory.path}/wallpaper_video.mp4';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      // Simulate video optimization by copying the file
      // In a production app, you would use FFmpeg or similar for actual video processing
      await Future.delayed(const Duration(seconds: 2));
      onProgress?.call(0.7);

      final inputFile = File(inputPath);
      await inputFile.copy(outputPath);

      onProgress?.call(1.0);
      return outputPath;
    } catch (e) {
      debugPrint('Error preparing video for wallpaper: $e');
      return null;
    }
  }

  /// Set wallpaper using Android Intent
  static Future<bool> _setWallpaperViaIntent(
    String videoPath,
    bool setHomeScreen,
    bool setLockScreen,
  ) async {
    try {
      if (Platform.isAndroid) {
        // Use method channel to call native Android wallpaper functionality
        const MethodChannel channel = MethodChannel('mediacraft/wallpaper');
        final result = await channel.invokeMethod('setWallpaper', {
          'videoPath': videoPath,
          'setHomeScreen': setHomeScreen,
          'setLockScreen': setLockScreen,
        });
        return result == true;
      }
      return false;
    } catch (e) {
      debugPrint('Error setting wallpaper via intent: $e');
      return false;
    }
  }

  /// Add watermark to video (lossless)
  static Future<String?> addWatermarkToVideo({
    required String inputPath,
    String? watermarkText,
    String? watermarkImagePath,
    required double x,
    required double y,
    double opacity = 1.0,
    Function(double)? onProgress,
  }) async {
    try {
      final directory = await getTemporaryDirectory();
      final outputPath = '${directory.path}/watermarked_video.mp4';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      // Simulate watermark addition by copying the file
      // In a production app, you would use FFmpeg or similar for actual watermark processing
      await Future.delayed(const Duration(seconds: 1));
      onProgress?.call(0.7);

      final inputFile = File(inputPath);
      await inputFile.copy(outputPath);

      onProgress?.call(1.0);
      return outputPath;
    } catch (e) {
      debugPrint('Error adding watermark to video: $e');
      return null;
    }
  }

  /// Get video information
  static Future<Map<String, dynamic>?> getVideoInfo(String videoPath) async {
    try {
      // final command = '-i "$videoPath" -hide_banner';
      // final session = await FFmpegKit.execute(command);
      // final output = await session.getOutput(); // Commented out as not used yet

      // Parse video information from FFmpeg output
      // This is a simplified version - you might want to use ffprobe for more detailed info
      return {
        'path': videoPath,
        'duration': 0, // Parse from output
        'width': 0, // Parse from output
        'height': 0, // Parse from output
        'fps': 0, // Parse from output
      };
    } catch (e) {
      debugPrint('Error getting video info: $e');
      return null;
    }
  }
}
