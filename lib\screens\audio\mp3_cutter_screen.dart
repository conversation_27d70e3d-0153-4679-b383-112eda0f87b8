import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:audioplayers/audioplayers.dart' as ap;
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:share_plus/share_plus.dart';

import '../../core/app_theme.dart';
import '../../providers/app_state_provider.dart';
import '../../providers/ad_provider.dart';
import '../../services/audio_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/file_picker_button.dart';

class Mp3CutterScreen extends StatefulWidget {
  const Mp3CutterScreen({super.key});

  @override
  State<Mp3CutterScreen> createState() => _Mp3CutterScreenState();
}

class _Mp3CutterScreenState extends State<Mp3CutterScreen> {
  String? _selectedAudioPath;
  ap.AudioPlayer? _audioPlayer;
  PlayerController? _waveformController;

  Duration _totalDuration = Duration.zero;
  Duration _currentPosition = Duration.zero;
  Duration _startTime = Duration.zero;
  Duration _endTime = Duration.zero;

  bool _isPlaying = false;
  bool _isLoading = false;
  String? _outputAudioPath;

  @override
  void initState() {
    super.initState();
    _audioPlayer = ap.AudioPlayer();
    _waveformController = PlayerController();

    _audioPlayer!.onDurationChanged.listen((duration) {
      setState(() {
        _totalDuration = duration;
        _endTime = duration;
      });
    });

    _audioPlayer!.onPositionChanged.listen((position) {
      setState(() {
        _currentPosition = position;
      });
    });

    _audioPlayer!.onPlayerStateChanged.listen((state) {
      setState(() {
        _isPlaying = state == ap.PlayerState.playing;
      });
    });
  }

  @override
  void dispose() {
    _audioPlayer?.dispose();
    _waveformController?.dispose();
    super.dispose();
  }

  Future<void> _pickAudio() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _selectedAudioPath = result.files.single.path;
          _outputAudioPath = null;
          _isLoading = true;
        });

        await _loadAudio();
      }
    } catch (e) {
      _showErrorSnackBar('Error picking audio: $e');
    }
  }

  Future<void> _loadAudio() async {
    if (_selectedAudioPath == null) return;

    try {
      await _audioPlayer!.setSourceDeviceFile(_selectedAudioPath!);
      await _waveformController!.preparePlayer(
        path: _selectedAudioPath!,
        shouldExtractWaveform: true,
      );

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Error loading audio: $e');
    }
  }

  Future<void> _playPause() async {
    if (_isPlaying) {
      await _audioPlayer!.pause();
    } else {
      await _audioPlayer!.resume();
    }
  }

  Future<void> _cutAudio() async {
    if (_selectedAudioPath == null) {
      _showErrorSnackBar('Please select an audio file first');
      return;
    }

    if (_startTime >= _endTime) {
      _showErrorSnackBar('Start time must be before end time');
      return;
    }

    final appState = Provider.of<AppStateProvider>(context, listen: false);
    final adProvider = Provider.of<AdProvider>(context, listen: false);

    try {
      appState.setLoading(true, task: 'Cutting audio...');

      final outputPath = await AudioService.cutAudio(
        inputPath: _selectedAudioPath!,
        startTime: _startTime,
        endTime: _endTime,
        onProgress: (progress) {
          appState.updateProgress(progress);
        },
      );

      appState.setLoading(false);

      if (outputPath != null) {
        setState(() {
          _outputAudioPath = outputPath;
        });

        _showSuccessSnackBar('Audio cut successfully!');

        // Show interstitial ad after successful completion
        await adProvider.showInterstitialAd();
      } else {
        _showErrorSnackBar('Failed to cut audio');
      }
    } catch (e) {
      appState.setLoading(false);
      _showErrorSnackBar('Error: $e');
    }
  }

  Future<void> _setAsRingtone() async {
    if (_outputAudioPath == null) {
      _showErrorSnackBar('Please cut the audio first');
      return;
    }

    try {
      final success = await AudioService.setAsRingtone(_outputAudioPath!);
      if (success) {
        _showSuccessSnackBar('Ringtone set successfully!');
      } else {
        _showErrorSnackBar('Failed to set ringtone');
      }
    } catch (e) {
      _showErrorSnackBar('Error setting ringtone: $e');
    }
  }

  Future<void> _shareAudio() async {
    if (_outputAudioPath == null) return;

    try {
      await Share.shareXFiles(
        [XFile(_outputAudioPath!)],
        text: 'Audio cut using MediaCraft',
      );
    } catch (e) {
      _showErrorSnackBar('Error sharing audio: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'MP3 Cutter & Ringtone Maker',
        subtitle: 'Cut audio files and create ringtones',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Audio Picker
            FilePickerButton(
              title: 'Select Audio File',
              subtitle: 'Choose an audio file to cut',
              icon: Icons.audiotrack,
              onTap: _pickAudio,
              selectedFile: _selectedAudioPath,
              color: AppTheme.accentColor,
            ),

            if (_selectedAudioPath != null) ...[
              const SizedBox(height: 24),
              if (_isLoading) ...[
                const Center(
                  child: CircularProgressIndicator(),
                ),
              ] else ...[
                // Waveform Display
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Audio Waveform',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Waveform
                        Container(
                          height: 80,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: AudioFileWaveforms(
                            size: const Size(double.infinity, 80),
                            playerController: _waveformController!,
                            waveformType: WaveformType.long,
                            playerWaveStyle: const PlayerWaveStyle(
                              fixedWaveColor: AppTheme.primaryColor,
                              liveWaveColor: AppTheme.accentColor,
                              spacing: 6,
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Audio Controls
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            IconButton(
                              onPressed: _playPause,
                              icon: Icon(
                                _isPlaying ? Icons.pause : Icons.play_arrow,
                                size: 32,
                              ),
                              style: IconButton.styleFrom(
                                backgroundColor: AppTheme.primaryColor,
                                foregroundColor: Colors.white,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Text(
                              '${_formatDuration(_currentPosition)} / ${_formatDuration(_totalDuration)}',
                              style: const TextStyle(
                                fontSize: 14,
                                color: AppTheme.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Time Selection
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Select Cut Range',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Start Time
                        Text(
                          'Start Time: ${_formatDuration(_startTime)}',
                          style: const TextStyle(fontSize: 14),
                        ),
                        Slider(
                          value: _startTime.inSeconds.toDouble(),
                          min: 0,
                          max: _totalDuration.inSeconds.toDouble(),
                          divisions: _totalDuration.inSeconds,
                          onChanged: (value) {
                            setState(() {
                              _startTime = Duration(seconds: value.toInt());
                              if (_startTime >= _endTime) {
                                _endTime =
                                    _startTime + const Duration(seconds: 1);
                              }
                            });
                          },
                          activeColor: AppTheme.primaryColor,
                        ),

                        // End Time
                        Text(
                          'End Time: ${_formatDuration(_endTime)}',
                          style: const TextStyle(fontSize: 14),
                        ),
                        Slider(
                          value: _endTime.inSeconds.toDouble(),
                          min: _startTime.inSeconds.toDouble() + 1,
                          max: _totalDuration.inSeconds.toDouble(),
                          divisions:
                              _totalDuration.inSeconds - _startTime.inSeconds,
                          onChanged: (value) {
                            setState(() {
                              _endTime = Duration(seconds: value.toInt());
                            });
                          },
                          activeColor: AppTheme.primaryColor,
                        ),

                        const SizedBox(height: 8),
                        Text(
                          'Selected Duration: ${_formatDuration(_endTime - _startTime)}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 32),

                // Cut Audio Button
                Consumer<AppStateProvider>(
                  builder: (context, appState, child) {
                    return ElevatedButton(
                      onPressed: appState.isLoading ? null : _cutAudio,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: appState.isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : const Text(
                              'Cut Audio',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    );
                  },
                ),

                // Action Buttons (if audio is cut)
                if (_outputAudioPath != null) ...[
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: _setAsRingtone,
                          icon: const Icon(Icons.ring_volume),
                          label: const Text('Set as Ringtone'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: _shareAudio,
                          icon: const Icon(Icons.share),
                          label: const Text('Share'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ],
          ],
        ),
      ),
    );
  }
}
