import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppStateProvider extends ChangeNotifier {
  bool _isLoading = false;
  String _currentTask = '';
  double _progress = 0.0;
  bool _isDarkMode = false;
  
  // Getters
  bool get isLoading => _isLoading;
  String get currentTask => _currentTask;
  double get progress => _progress;
  bool get isDarkMode => _isDarkMode;

  AppStateProvider() {
    _loadPreferences();
  }

  // Loading state management
  void setLoading(bool loading, {String task = ''}) {
    _isLoading = loading;
    _currentTask = task;
    if (!loading) {
      _progress = 0.0;
    }
    notifyListeners();
  }

  void updateProgress(double progress) {
    _progress = progress.clamp(0.0, 1.0);
    notifyListeners();
  }

  void setTask(String task) {
    _currentTask = task;
    notifyListeners();
  }

  // Theme management
  void toggleTheme() {
    _isDarkMode = !_isDarkMode;
    _savePreferences();
    notifyListeners();
  }

  void setTheme(bool isDark) {
    _isDarkMode = isDark;
    _savePreferences();
    notifyListeners();
  }

  // Preferences management
  Future<void> _loadPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isDarkMode = prefs.getBool('isDarkMode') ?? false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading preferences: $e');
    }
  }

  Future<void> _savePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isDarkMode', _isDarkMode);
    } catch (e) {
      debugPrint('Error saving preferences: $e');
    }
  }

  // Task completion tracking
  void completeTask() {
    _isLoading = false;
    _currentTask = '';
    _progress = 1.0;
    notifyListeners();
    
    // Reset progress after a short delay
    Future.delayed(const Duration(seconds: 1), () {
      _progress = 0.0;
      notifyListeners();
    });
  }

  // Error handling
  void setError(String error) {
    _isLoading = false;
    _currentTask = 'Error: $error';
    _progress = 0.0;
    notifyListeners();
    
    // Clear error after delay
    Future.delayed(const Duration(seconds: 3), () {
      _currentTask = '';
      notifyListeners();
    });
  }
}
