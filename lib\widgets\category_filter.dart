import 'package:flutter/material.dart';

import '../core/app_theme.dart';
import '../models/tool_model.dart';

class CategoryFilter extends StatelessWidget {
  final ToolCategory? selectedCategory;
  final Function(ToolCategory?) onCategorySelected;

  const CategoryFilter({
    super.key,
    required this.selectedCategory,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: ListView(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        children: [
          _buildCategoryChip(
            label: 'All',
            icon: Icons.apps,
            isSelected: selectedCategory == null,
            onTap: () => onCategorySelected(null),
            color: AppTheme.primaryColor,
          ),
          const SizedBox(width: 12),
          _buildCategoryChip(
            label: 'Video',
            icon: Icons.videocam,
            isSelected: selectedCategory == ToolCategory.video,
            onTap: () => onCategorySelected(ToolCategory.video),
            color: const Color(0xFF6366F1),
          ),
          const SizedBox(width: 12),
          _buildCategoryChip(
            label: 'Audio',
            icon: Icons.audiotrack,
            isSelected: selectedCategory == ToolCategory.audio,
            onTap: () => onCategorySelected(ToolCategory.audio),
            color: const Color(0xFF06B6D4),
          ),
          const SizedBox(width: 12),
          _buildCategoryChip(
            label: 'Image',
            icon: Icons.image,
            isSelected: selectedCategory == ToolCategory.image,
            onTap: () => onCategorySelected(ToolCategory.image),
            color: const Color(0xFFF59E0B),
          ),
          const SizedBox(width: 12),
          _buildCategoryChip(
            label: 'PDF',
            icon: Icons.picture_as_pdf,
            isSelected: selectedCategory == ToolCategory.pdf,
            onTap: () => onCategorySelected(ToolCategory.pdf),
            color: const Color(0xFF8B5CF6),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip({
    required String label,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? color : color.withOpacity(0.3),
            width: 1.5,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: color.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: isSelected ? Colors.white : color,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.white : color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
